#!/usr/bin/env python3
"""
Analytics Request Script
Makes a POST request to the WispByte analytics API and prints the response.
"""

import requests
import json
import sys
from datetime import datetime

def make_analytics_request():
    """Make the analytics request and print the response"""
    
    # URL from the curl command
    url = 'https://analytics.wispbyte.com/api/event'
    
    # Headers from the curl command
    headers = {
        'sec-ch-ua-platform': '"Windows"',
        'Referer': 'https://wispbyte.com/',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        'Content-Type': 'text/plain',
        'sec-ch-ua-mobile': '?0'
    }
    
    # Data from the curl command
    data = '{"n":"pageview","u":"https://wispbyte.com/client/dashboard","d":"wispbyte.com","r":"https://wispbyte.com/client/servers/abe58846/console"}'
    
    print(f"[{datetime.now()}] Making request to analytics API...")
    print(f"URL: {url}")
    print(f"Headers: {json.dumps(headers, indent=2)}")
    print(f"Data: {data}")
    print("-" * 60)
    
    try:
        # Make the POST request
        response = requests.post(
            url,
            headers=headers,
            data=data,
            timeout=30
        )
        
        # Print response details
        print(f"Response Status Code: {response.status_code}")
        print(f"Response Headers: {json.dumps(dict(response.headers), indent=2)}")
        print("-" * 60)
        
        # Print response content
        print("Response Content:")
        if response.text:
            try:
                # Try to parse as JSON for pretty printing
                response_json = response.json()
                print(json.dumps(response_json, indent=2))
            except json.JSONDecodeError:
                # If not JSON, print as text
                print(response.text)
        else:
            print("(Empty response)")
        
        print("-" * 60)
        print(f"Request completed successfully!")
        
        return response
        
    except requests.exceptions.RequestException as e:
        print(f"Error making request: {e}")
        return None
    except Exception as e:
        print(f"Unexpected error: {e}")
        return None

def main():
    """Main function"""
    print("WispByte Analytics Request Script")
    print("=" * 60)
    
    response = make_analytics_request()
    
    if response:
        print(f"\nFinal Status: {'SUCCESS' if response.status_code == 200 else 'FAILED'}")
    else:
        print("\nFinal Status: FAILED")

if __name__ == "__main__":
    main()
