#!/usr/bin/env python3
"""
Server <PERSON><PERSON> Script
Automatically renews a free server every 1 hour and 1 minute by making API calls.
"""

import requests
import time
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('server_renew.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

# Set console encoding to handle unicode
import sys
if sys.platform == 'win32':
    import os
    os.system('chcp 65001 > nul')

logger = logging.getLogger(__name__)

class ServerRenewer:
    def __init__(self):
        self.url = 'https://gpanel.eternalzero.cloud/api/client/freeservers/9e26fee7-e4cf-4440-bb2a-b2bd698a6075/renew'
        self.headers = {
            'accept': 'application/json',
            'accept-language': 'en-US,en;q=0.9',
            'content-type': 'application/json',
            'origin': 'https://gpanel.eternalzero.cloud',
            'priority': 'u=1, i',
            'referer': 'https://gpanel.eternalzero.cloud/server/9e26fee7',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'x-requested-with': 'XMLHttpRequest',
            'x-xsrf-token': 'eyJpdiI6IjdTVEpWY1I2cXBlTitFSHcrWmdUN3c9PSIsInZhbHVlIjoidTI2OHlnT3d6TG83UzVNdS9rcDFTVUU2Q1doNWlFYlRuT1QzSjErZnJOQVlRQk1vaHVoTmQrbkY1SVhUbDROYkZxZkxtam9oYnNQVXgyZFpraVoyRU1kQmxmOUt3VWNGenNvMEtkb2ZHZ20xUEU1L0lHSTg3eWN3N09wZU9BczMiLCJtYWMiOiI3NGFhM2U4MDllNjcwODM3YjkwOWY5NzQ0MGFhZDkyMTg3NDAwMjE5ZWQxN2QzYWY2Zjk0YjgxYzIzMjU4ZDE1IiwidGFnIjoiIn0='
        }
        self.cookies = {
            'XSRF-TOKEN': 'eyJpdiI6IjdTVEpWY1I2cXBlTitFSHcrWmdUN3c9PSIsInZhbHVlIjoidTI2OHlnT3d6TG83UzVNdS9rcDFTVUU2Q1doNWlFYlRuT1QzSjErZnJOQVlRQk1vaHVoTmQrbkY1SVhUbDROYkZxZkxtam9oYnNQVXgyZFpraVoyRU1kQmxmOUt3VWNGenNvMEtkb2ZHZ20xUEU1L0lHSTg3eWN3N09wZU9BczMiLCJtYWMiOiI3NGFhM2U4MDllNjcwODM3YjkwOWY5NzQ0MGFhZDkyMTg3NDAwMjE5ZWQxN2QzYWY2Zjk0YjgxYzIzMjU4ZDE1IiwidGFnIjoiIn0%3D',
            'pterodactyl_session': 'eyJpdiI6Imw3TWNxZkQ5a0RBaThDek0xc2dYbEE9PSIsInZhbHVlIjoiSzF0THZyVjRyQS85ZWt1OURkdmJqbVVlMG1UbG8vclVDcEV0c1VETXdHeEtGajlHMk93ZDVRd1kzRFg5SzRVd2poSS9EK1RqOHZiU3VYbURUK0pvODR6RGVXdW1YM0VLT0w2YWQxSUhCWGltVjFoM25jeWR4OWRWUFFIZzUxM1kiLCJtYWMiOiI5NWE5OTgzNTA4MjhkNTNkMTZkZjZiNmY4ODFjYTczOWNmNGE3OWNkYzZiOWYyYWMzODg5Y2E4Y2Y0NjQ1MGVjIiwidGFnIjoiIn0%3D'
        }
        self.data = {}  # Empty JSON object as per curl --data-raw '{}'

        # Interval: 1 hour and 1 minute = 61 minutes = 3660 seconds
        self.interval_seconds = 61 * 60

    def make_renewal_request(self):
        """Make the server renewal request"""
        try:
            logger.info("Making server renewal request...")

            response = requests.post(
                self.url,
                headers=self.headers,
                cookies=self.cookies,
                json=self.data,
                timeout=30
            )

            # Log the response
            logger.info(f"Response status code: {response.status_code}")
            logger.info(f"Response headers: {dict(response.headers)}")

            if response.status_code == 200:
                logger.info("[SUCCESS] Server renewal request successful!")
                try:
                    response_data = response.json()
                    logger.info(f"Response data: {json.dumps(response_data, indent=2)}")
                except json.JSONDecodeError:
                    logger.info(f"Response text: {response.text}")
            else:
                logger.warning(f"[WARNING] Server renewal request returned status {response.status_code}")
                logger.warning(f"Response text: {response.text}")

            return response.status_code == 200

        except requests.exceptions.RequestException as e:
            logger.error(f"[ERROR] Error making renewal request: {e}")
            return False
        except Exception as e:
            logger.error(f"[ERROR] Unexpected error: {e}")
            return False

    def run_scheduler(self):
        """Run the renewal scheduler"""
        logger.info("[START] Starting server renewal scheduler...")
        logger.info(f"[SCHEDULE] Will renew server every {self.interval_seconds // 60} minutes")

        # Make initial request
        self.make_renewal_request()

        # Schedule subsequent requests
        while True:
            try:
                # Calculate next run time by adding 61 minutes to current time
                from datetime import timedelta
                next_run = datetime.now() + timedelta(seconds=self.interval_seconds)
                next_run = next_run.replace(microsecond=0, second=0)

                logger.info(f"[SCHEDULE] Next renewal scheduled for: {next_run}")
                logger.info(f"[SLEEP] Sleeping for {self.interval_seconds} seconds...")

                time.sleep(self.interval_seconds)
                self.make_renewal_request()

            except KeyboardInterrupt:
                logger.info("[STOP] Scheduler stopped by user")
                break
            except Exception as e:
                logger.error(f"[ERROR] Error in scheduler: {e}")
                logger.info(f"[RETRY] Continuing with next iteration...")
                time.sleep(60)  # Wait 1 minute before retrying

def main():
    """Main function"""
    renewer = ServerRenewer()
    renewer.run_scheduler()

if __name__ == "__main__":
    main()
